
resource "aws_s3_bucket" "portfolio_bucket" {
  bucket = "manikanta-portfolio"
  tags = {
    Name        = "manikanta-portfolio"
    Environment = "production"
  }
}

resource "aws_s3_bucket_website_configuration" "example" {
  bucket = aws_s3_bucket.manikanta_portfolio.id

  index_document {
    suffix = "index.html"
  }

  error_document {
    key = "error.html"
  }

  routing_rule {
    condition {
      key_prefix_equals = "docs/"
    }
    redirect {
      replace_key_prefix_with = "documents/"
    }
  }
}
